package desk

import (
	"fwyytool/api/arkgo"
	"github.com/gin-gonic/gin"
)

var ArkDataDiffService arkDataDiffService

type arkDataDiffService struct {
}

func (s arkDataDiffService) GetDiffRes(ctx *gin.Context, timeRange int64, handler string) (data arkgo.GetArkStudentDataDiffResp, err error) {
	arkTestCaseDetail, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, arkgo.GetArkStudentDataDiffResParams{TimeRange: timeRange, Handler: handler})
	if err != nil {
		return
	}
	return arkTestCaseDetail, nil
}
