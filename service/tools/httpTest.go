package tools

import (
	"bytes"
	"compress/gzip"
	"errors"
	"fmt"
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/controllers/http/tools/output"
	"fwyytool/libs/json"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

var HttpTestService httpTestService

type httpTestService struct {
}

func (s httpTestService) Call(ctx *gin.Context, param toolsInput.HttpTestCallParam) (data output.HttpTestCallOutput, err error) {
	data = output.HttpTestCallOutput{}
	header := http.Header{}

	headerArr := strings.Split(param.Header, "\n")
	for _, headerStr := range headerArr {
		headerDetail := strings.Split(headerStr, ":")
		if len(headerDetail) < 2 {
			continue
		}

		header.Set(headerDetail[0], strings.TrimSpace(strings.Join(headerDetail[1:], ":")))
	}
	header.Add("content-type", param.ContentType)
	header.Add("cookie", param.Cookie)

	respData, err := s.httpDo(ctx, param.Url, param.Method, param.Body, header)
	if err != nil {
		return
	}
	data.Body = respData.Body
	data.Detail.HttpCode = int64(respData.StatusCode)
	data.Detail.Method = respData.Request.Method
	data.Detail.Proto = respData.Request.Proto
	data.Detail.Url = respData.Request.URL.String()

	respHeaderArr := make([]string, 0)
	for k, v := range respData.Header {
		respHeaderArr = append(respHeaderArr, fmt.Sprintf("%s:%s", k, strings.Join(v, "")))
	}
	data.Header, _ = json.MarshalToString(respHeaderArr)
	return
}

type httpResp struct {
	Body       string
	StatusCode int
	Request    *http.Request
	Header     http.Header
}

func (s httpTestService) httpDo(ctx *gin.Context, remoteUrl string, method string, queryRaw string, header http.Header) (respData httpResp, err error) {
	respData = httpResp{}
	if len(remoteUrl) == 0 || len(method) == 0 {
		return respData, errors.New("url、method不能为空")
	}
	client := &http.Client{}

	u, err := url.ParseRequestURI(remoteUrl)
	if method == http.MethodGet {
		urlValue, err := url.ParseQuery(queryRaw)
		if err != nil {
			return respData, err
		}
		u.RawQuery = urlValue.Encode()
		queryRaw = ""
	}

	body := ioutil.NopCloser(strings.NewReader(queryRaw))
	req, err := http.NewRequest(method, u.String(), body)
	if err != nil {
		return
	}
	req.Header = header

	resp, err := client.Do(req)
	if err != nil {
		return
	}

	defer resp.Body.Close()

	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}
	respData.Body = string(b)
	respData.Request = resp.Request
	respData.Header = resp.Header
	respData.StatusCode = resp.StatusCode
	return
}

// Router HTTP代理转发方法
func (s httpTestService) Router(ctx *gin.Context, param toolsInput.HttpTestRouterParam) error {
	// 解析目标URL
	targetURL, err := url.Parse(param.Domain)
	if err != nil {
		return errors.New("invalid target URL")
	}

	// 获取原始请求的查询参数，排除domain参数
	originalQuery := ctx.Request.URL.Query()
	originalQuery.Del("domain")

	// 如果目标URL已有查询参数，合并它们
	if targetURL.RawQuery != "" {
		targetQuery, _ := url.ParseQuery(targetURL.RawQuery)
		for key, values := range originalQuery {
			for _, value := range values {
				targetQuery.Add(key, value)
			}
		}
		targetURL.RawQuery = targetQuery.Encode()
	} else {
		targetURL.RawQuery = originalQuery.Encode()
	}

	// 处理请求体内容
	var bodyBytes []byte
	var body io.Reader

	// 检查Content-Type来决定如何处理请求体
	contentType := ctx.GetHeader("Content-Type")

	if ctx.Request.Method == "POST" || ctx.Request.Method == "PUT" || ctx.Request.Method == "PATCH" {
		if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			// 对于form编码，将查询参数转换为请求体
			if len(originalQuery) > 0 {
				bodyBytes = []byte(originalQuery.Encode())
				body = bytes.NewReader(bodyBytes)
				// 清空URL查询参数，因为已经放到body中了
				targetURL.RawQuery = ""
			}
		} else {
			// 对于其他类型（JSON等），直接读取原始请求体
			if ctx.Request.Body != nil {
				bodyBytes, err = io.ReadAll(ctx.Request.Body)
				if err != nil {
					return fmt.Errorf("failed to read request body: %v", err)
				}
				if len(bodyBytes) > 0 {
					body = bytes.NewReader(bodyBytes)
				}
			}
		}
	} else {
		// GET等方法，参数保留在URL中
		// 已经在前面处理了查询参数合并
	}

	// 创建新的context，避免使用原始请求的context
	req, err := http.NewRequest(ctx.Request.Method, targetURL.String(), body)
	if err != nil {
		return errors.New("failed to create proxy request")
	}

	// 复制所有请求头，但跳过一些可能导致问题的头部
	for key, values := range ctx.Request.Header {
		// 跳过一些可能导致问题的头部
		lowerKey := strings.ToLower(key)
		if lowerKey == "content-length" || lowerKey == "accept-encoding" {
			continue
		}
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 设置Content-Type和Content-Length
	if len(bodyBytes) > 0 {
		req.ContentLength = int64(len(bodyBytes))
		// 如果是form编码且原来没有Content-Type，设置正确的Content-Type
		if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		}
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("proxy request failed: %v", err)
	}
	defer resp.Body.Close()

	// 处理响应体，检查是否为gzip压缩
	var responseBody io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to create gzip reader: %v", err)
		}
		defer gzipReader.Close()
		responseBody = gzipReader

		// 移除gzip相关的响应头，因为我们已经解压了
		resp.Header.Del("Content-Encoding")
		resp.Header.Del("Content-Length")
	}

	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			ctx.Header(key, value)
		}
	}

	// 设置响应状态码
	ctx.Status(resp.StatusCode)

	// 流式复制响应体
	_, err = io.Copy(ctx.Writer, responseBody)
	if err != nil {
		return fmt.Errorf("failed to copy response body: %v", err)
	}

	return nil
}
