package arkgo

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.ArkGo,
	}
	return c
}

const (
	getArkDetailByCnameAPI             = "/arkgo/tool/getarkdetailbycname"
	getArkDetailByRuleIdAPI            = "/arkgo/tool/getarkdetailbyruleid"
	getArkFeatureToolDetailByToolIdAPI = "/arkgo/tool/getarkfeaturetooldetailbytoolid"
	getArkDataDiffResAPI               = "/arkgo/tool/getdiffres"
	getArkFusingResAPI                 = "/arkgo/tool/getarkfusing"
	getDiffReportAPI                   = "/arkgo/tool/getdiffreport"
)

func (c *Client) GetDiffReportRes(ctx *gin.Context) (resp map[string]GetDataDiffReportOutPut, err error) {

	opts := base.HttpRequestOptions{}
	utils.DecorateHttpOptions(ctx, &opts)
	res, err := conf.API.ArkGo.HttpGet(ctx, getDiffReportAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetArkStudentDataDiffRes(ctx *gin.Context, params GetArkStudentDataDiffResParams) (resp GetArkStudentDataDiffResp, err error) {
	resp = GetArkStudentDataDiffResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.ArkGo.HttpGet(ctx, getArkDataDiffResAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetArkDetailByCname(ctx *gin.Context, params GetArkDetailByCnameParams) (resp GetArkDetailResp, err error) {
	resp = GetArkDetailResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.ArkGo.HttpGet(ctx, getArkDetailByCnameAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetArkDetailByRuleId(ctx *gin.Context, params GetArkDetailByRuleIdParams) (resp GetArkDetailResp, err error) {
	resp = GetArkDetailResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.ArkGo.HttpGet(ctx, getArkDetailByRuleIdAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetArkFeatureToolDetailByToolId(ctx *gin.Context, params GetArkFeatureToolDetailByToolIdParams) (resp GetArkFeatureToolDetailResp, err error) {
	resp = GetArkFeatureToolDetailResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.ArkGo.HttpGet(ctx, getArkFeatureToolDetailByToolIdAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetArkFusing(ctx *gin.Context) (resp GetArkFusingResp, err error) {
	opts := base.HttpRequestOptions{Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.ArkGo.HttpGet(ctx, getArkFusingResAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}
