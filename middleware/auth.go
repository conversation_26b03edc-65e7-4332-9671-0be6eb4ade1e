package middleware

import (
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/http"
)

const LOGIN_USER_INFO = "userInfo"

// 获取登录信息
func Auth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		zlog.Infof(ctx, "auth, GetEnvName:%s", utils.GetEnvName(ctx))

		if utils.GetEnvName(ctx) != "online" && utils.GetEnvName(ctx) != "tips" {
			ctx.Next()
			return
		}

		userId := 0
		userInfoI, ok := ctx.Get(LOGIN_USER_INFO)
		if !ok {
			cookies := ctx.Request.Cookies()
			if len(cookies) == 0 {
				ctx.Redirect(http.StatusFound, components.Util.GetLoginUrl(ctx))
				ctx.Abort()
				return
			}

			loginUserId, logErr := userprofile.NewClient().LoginCheck(ctx)
			if logErr != nil {
				ctx.Redirect(http.StatusFound, components.Util.GetLoginUrl(ctx))
				ctx.Abort()
				return
			}

			user, err := userprofile.NewClient().GetUserInfo(ctx, loginUserId)
			if err != nil {
				ctx.Redirect(http.StatusFound, components.Util.GetLoginUrl(ctx))
				ctx.Abort()
				return
			}
			ctx.Set(LOGIN_USER_INFO, user)
			userId = user.UserId
		} else {
			userInfo := userInfoI.(*userprofile.UserInfo)
			userId = userInfo.UserId
		}

		if userId <= 0 {
			ctx.Redirect(http.StatusFound, components.Util.GetLoginUrl(ctx))
			ctx.Abort()
			return
		}

		zlog.Infof(ctx, "auth, userId:%d, userIds:%+v", userId, conf.Custom.Auth.UserIDs)

		if exist, _ := fwyyutils.InArrayInt64(int64(userId), conf.Custom.Auth.UserIDs); !exist {
			base.RenderJsonFail(ctx, components.LoginErr("Auth:无权限"))
			ctx.Abort()
			return
		}
		ctx.Next()
	}

}

func StrictAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if utils.GetEnvName(ctx) != "online" && utils.GetEnvName(ctx) != "tips" {
			ctx.Next()
			return
		}

		user, _ := ctx.Get(LOGIN_USER_INFO)
		userInfo := user.(*userprofile.UserInfo)
		userId := userInfo.UserId

		if userId <= 0 {
			ctx.Redirect(http.StatusFound, components.Util.GetLoginUrl(ctx))
			ctx.Abort()
			return
		}

		zlog.Infof(ctx, "auth, userId:%d, userIds:%+v", userId, conf.Custom.Auth.StrictUserIDs)

		if exist, _ := fwyyutils.InArrayInt64(int64(userId), conf.Custom.Auth.StrictUserIDs); !exist {
			base.RenderJsonFail(ctx, components.LoginErr("StrictAuth:无权限"))
			ctx.Abort()
			return
		}
	}
}
