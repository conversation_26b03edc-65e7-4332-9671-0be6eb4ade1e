package components

import (
	"fmt"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
	"net/http"
	"path"
	"time"
)

type util struct {
}

var Util util

func (s util) GetShipEnvNameFromRemote(ctx *gin.Context) string {
	res, err := conf.API.Fwyytool.HttpGet(ctx, "/mkanswersheet/sys/shipenvname", base.HttpRequestOptions{})
	if err != nil {
		zlog.Infof(ctx, "http get err: %v", err)
		return ""
	}
	var data struct {
		Name string `json:"name"`
	}
	_ = jsoniter.Unmarshal(res.Response, &data)
	return data.Name
}

// GetEnvName 获取ship环境容器名 SHIP_ENV_NAME
func (s util) GetEnvName(ctx *gin.Context) (name string) {
	name = utils.GetEnvName(ctx)
	if name == "" {
		name = s.GetShipEnvNameFromRemote(ctx)
	}
	return name
}

func (s util) DownloadFile(ctx *gin.Context, fileUrl string) (fileName string, err error) {
	resp, err := http.Get(fileUrl)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}

	fileName = path.Base(fileUrl)

	err = ioutil.WriteFile(fileName, data, 0644)
	if err != nil {
		return "", err
	}

	return fileName, nil
}

func (s util) ChunkArrayInt(array []int, size int) [][]int {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func (s util) InArrayString(e string, array []string) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func (s util) GetTodayTimestamp() int64 {
	t := time.Now()
	todayTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).Unix()
	return todayTime
}

func (s util) GetLoginUrl(ctx *gin.Context) string {
	host := ctx.Request.Host
	refer := host + ctx.Request.RequestURI
	return fmt.Sprintf("https://ips.zuoyebang.cc/static/cas-fe/?version=2.0&sdk=odp&sid=userprofile&service=//%s/assistantdesk/public/login?refer=https://%s&timestamp=%d", host, refer, time.Now().UnixMilli())
}
