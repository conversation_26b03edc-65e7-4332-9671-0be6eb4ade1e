package components

import (
	"fmt"
	"fwyytool/helpers"
	"github.com/gin-gonic/gin"
)

var Redis redis

type redis struct {
}

const (
	ChromeNumExpireTime = 120
)
const (
	//当前启动的chrome数量
	RunChromeNumKey = "MarkfwyytoolRunChromeNum_%d"
)

func (s redis) GetRedisKeyWithEnv(ctx *gin.Context, key string) string {
	return key + "_" + Util.GetEnvName(ctx)
}

func (s redis) GetRunChromeNumKey(ctx *gin.Context, podID int) string {
	return s.GetRedisKeyWithEnv(ctx, fmt.Sprintf(RunChromeNumKey, podID))
}

func (s redis) IncrRunChromeNum(ctx *gin.Context) (num int64, err error) {
	key := s.GetRunChromeNumKey(ctx, helpers.PodID)
	if num, err = helpers.RedisClient.Incr(ctx, key); err != nil {
		return
	}
	_, _ = helpers.RedisClient.Expire(ctx, key, ChromeNumExpireTime)
	return
}

func (s redis) DecrRunChromeNum(ctx *gin.Context) (num int64, err error) {
	key := s.GetRunChromeNumKey(ctx, helpers.PodID)
	if _, err = helpers.RedisClient.Decr(ctx, key); err != nil {
		return
	}
	_, _ = helpers.RedisClient.Expire(ctx, key, ChromeNumExpireTime)
	return
}

func (s redis) GetRunPdfToPngNumKey(ctx *gin.Context, podID int) string {
	return s.GetRedisKeyWithEnv(ctx, fmt.Sprintf(RunChromeNumKey, podID))
}

func (s redis) IncrRunPdfToPngNum(ctx *gin.Context) (num int64, err error) {
	key := s.GetRunPdfToPngNumKey(ctx, helpers.PodID)
	if num, err = helpers.RedisClient.Incr(ctx, key); err != nil {
		return
	}
	_, _ = helpers.RedisClient.Expire(ctx, key, ChromeNumExpireTime)
	return
}

func (s redis) DecrRunPdfToPngNum(ctx *gin.Context) (num int64, err error) {
	key := s.GetRunPdfToPngNumKey(ctx, helpers.PodID)
	if _, err = helpers.RedisClient.Decr(ctx, key); err != nil {
		return
	}
	_, _ = helpers.RedisClient.Expire(ctx, key, ChromeNumExpireTime)
	return
}
