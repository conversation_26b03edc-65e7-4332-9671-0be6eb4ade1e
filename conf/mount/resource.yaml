mysql:
  default:
    service: default
    database: homework_fudao
    addr: *************:3306
    user: homework
    password: homework
    maxidleconns: 50
    maxopenconns: 100
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
    writeTimeOut: 3s
    readTimeOut: 3s

clickhouse:
  default:
    service: default
    database: mysql_slow
    addr: localhost:9000
    username: root
    password: 123456

redis:
  bzrfd:
    service: redis-bzrfd
    addr: redis-qatest-svc.redis:6379
    maxIdle: 10
    maxActive: 200
    idleTimeout: 3s
    connTimeOut: 1s
    readTimeOut: 1s
    writeTimeOut: 1s

elastic:
  default:
    service: default
    #    addr: "http://**************:9205,http://**************:9205,http://**************:9205"
    addr: "http://**************:9200"
    username: "kunpeng_app"
    password: "kunpeng_app"
    gzip: false

cos:
  image:
    bucket: zyb-image10
    app_id: "1253445850"
    secret_id: AKIDKYbxDMT9xb3Nae8KuIUedT8br5EDkd7a
    secret_key: QeUJzYxAAbt0vgbX5VqAJG3Rp472mLl3
    # region 规范使用，默认下zyb的bucket都是 ap-beijing ,如果有问题可以联系我！
    region: ap-beijing
    picture_region: picbj
    filesize_limit: 1048576
    cloud: tencent
    thumbnail: 1
    file_prefix: zyb10_
    directory: "offline"
    is_public: 1
  #internal用于内部保存pdf文件等，部署环境中使用的bucket/secret_id/secret_key是对的，提交的代码复用上面的image配置
  mkinteral:
    bucket: zyb-kunpeng-mkinteral
    app_id: "1253445850"
    secret_id: AKID1xoRnGVhnfYRDyc5Ro4FSkurb6MOCo5G
    secret_key: teIjxxwnX6WoAWjkiaGj0DTn2LPFya0I
    region: ap-beijing
    #答题卡的压缩包大小限制: 3000M
    filesize_limit: 3145728000
    cloud: tencent
    thumbnail: 1
    file_prefix: mkinteral_
    is_public:
  mkstatic:
    bucket: zyb-kunpeng-mkstatic
    app_id: "1253445850"
    secret_id: AKIDTkslNima7NM4VgaEckbCYeT1JK3WkhMl
    secret_key: A3f04sQidLnytYx5ze5HFyTjlZ8UcVAb
    region: ap-beijing
    cloud: tencent
    thumbnail: 1
    file_prefix: mkstatic_

rmq:
  producer:
    # service: producer 名称，不同 producer 间不可重复
    - service: fwyytool
      # 提供名字服务器的地址，eg: ship-rocketmq-fwyytool-svc.mq
      nameserver: **************:8077
      # 需要生产信息的topic名称
      topic: mark
      # 消息生产失败的重试次数 默认不重试
      retry: 3
      # 消息生产的超时时间 默认1s
      timeout: 1000ms

  consumer:
    # service: consumer 名称，不同 consumer 间不可重复
    - service: fwyytool
      # 提供名字服务器的地址，eg: ship-rocketmq-fwyytool-svc.mq
      nameserver: **************:8077
      # 需要消费信息的topic名称
      topic: mark
      # 消费组名称, 不同服务间要保持名称唯一
      group: mark_fwyytool_make
      # 要消费消息的标签, 为空的话则会默认消费所有消息
      tags:
        - "TagIDExamfwyytool"
      # 是否是广播消费模式 广播消费模式下每个消费者实例会全量消费所有的消息, 而集群模式下每个消费者实例会竞争消费每条消息
      broadcast: false
      # 顺序消费 对消息的处理顺序有要求的业务需要设置为true, false代表使用并发消费模式
      orderly: false
      # 消费失败重试次数, 默认不重试
      retry: 3