package conf

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/cos"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/redis"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/server/http"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"golang.org/x/time/rate"
)

const App = "fwyytool"

// DefaultServiceName 资源服务的默认服务名
const DefaultServiceName = "default"
const RedisDefaultServiceName = "bzrfd"

// ShadowServiceName 影子资源服务的默认服务名
const ShadowServiceName = "shadow"

const defaultJWTExpirationHours = 72

// 配置文件对应的全局变量
var (
	BasicConf TBasic
	API       TApi
	RConf     ResourceConf
	Custom    TCustom
)

// TBasic 基础配置,对应config.yaml
type TBasic struct {
	Pprof  base.PprofConfig
	Log    zlog.LogConfig
	Server http.ServerConfig
	// ....业务可扩展其他简单的配置
}

// TApi 对应 api.yaml
type TApi struct {
	Fwyytool        *base.ApiClient `yaml:"fwyytool"`
	OfficeServer    *base.ApiClient `yaml:"officeserver"`
	Tower           *base.ApiClient `yaml:"tower"`
	Genke           *base.ApiClient `yaml:"genke"`
	AssistantDesk   *base.ApiClient `yaml:"assistantdesk"`
	AssistantDeskGo *base.ApiClient `yaml:"assistantdeskgo"`
	ZbCore          *base.ApiClient `yaml:"zbcore"`
	ZbCoreDal       *base.ApiClient `yaml:"zbcoredal"`
	ZbCoreDau       *base.ApiClient `yaml:"zbcoredau"`
	ZbCoreDat       *base.ApiClient `yaml:"zbcoredat"`
	ArkGo           *base.ApiClient `yaml:"arkgo"`
	UserProfile     *base.ApiClient `yaml:"userprofile"`
	AssistantCourse *base.ApiClient `yaml:"assistantcourse"`
	Moat            *base.ApiClient `yaml:"moat"`
	Mesh            *base.ApiClient `yaml:"mesh"`
	CourseBase      *base.ApiClient `yaml:"coursebase"`
	CourseSearch    *base.ApiClient `yaml:"coursesearch"`
	KunPeng         *base.ApiClient `yaml:"kunpeng"`
	KPStaff         *base.ApiClient `yaml:"kpstaff"`
	Intratraffic    *base.ApiClient `yaml:"intratraffic"`
	TouchMis        *base.ApiClient `yaml:"touchmis"`
	TouchMisGo      *base.ApiClient `yaml:"touchmisgo"`
}

// rate单机限流配置
type RateItem struct {
	Limit  rate.Limit `yaml:"limit"`  // 每秒写入数，浮点型(float64),根据这个计数生产token的间隔时间，并发控制数
	Bursts int        `yaml:"bursts"` // 最大的token数目，也就是桶的容量，初始化时时满容量的，最大并发数
}

// TCustom custom.yaml
type TCustom struct {
	Rate      map[string]RateItem `yaml:"rate"` // 单机限流令牌桶配置
	Mesh      Mesh                `yaml:"mesh"`
	Auth      Auth                `yaml:"auth"`
	DingToken map[string]string   `yaml:"dingToken"`
}

type Mesh struct {
	AppId    string `yaml:"appId"`
	ModuleId string `yaml:"moduleId"`
	GroupId  []int  `yaml:"groupId"`
}

type Auth struct {
	StrictUserIDs []int64 `yaml:"strictUserIDs"`
	UserIDs       []int64 `yaml:"userIDs"`
}

type WordConvertConfig struct {
	ClientId             int    `yaml:"client_id" json:"client_id"`
	ClientSecret         string `yaml:"client_secret" json:"client_secret"`
	PlId                 string `yaml:"pl_id" json:"pl_id"`
	ProdType             string `yaml:"prod_type" json:"prod_type"`
	OrderSource          int    `yaml:"order_source" json:"order_source"`
	Type                 string `yaml:"type" json:"type"`
	WordStorageDirectory string `yaml:"word_storage_directory" json:"word_storage_directory"`
}

// JWTConfig represents an application jwt configuration.
type JWTConfig struct {
	// JWT signing key. required.
	SigningKey string `yaml:"signingKey"`
	// JWT expiration in hours. Defaults to 72 hours (3 days)
	Expiration int `yaml:"expiration"`
}

// ResourceConf 对应 resource.yaml
type ResourceConf struct {
	Redis      map[string]redis.RedisConf
	Mysql      map[string]base.MysqlConf
	ClickHouse map[string]ClickHouseConf
	Elastic    map[string]base.ElasticClientConfig
	Cos        map[string]cos.BucketConfig
	Rmq        rmq.RmqConfig
	RmqNew     rmq.RmqConfig `yaml:"rmqNew"` // rmq集群迁移期间使用，迁移完成后删掉
}

type ClickHouseConf struct {
	Service  string `yaml:"service"`
	Database string `yaml:"database"`
	Addr     string `yaml:"addr"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

func InitConf() {
	// 加载通用基础配置（必须）
	env.LoadConf("config.yaml", env.SubConfMount, &BasicConf)

	// 加载api调用相关配置（optional）
	env.LoadConf("api.yaml", env.SubConfMount, &API)

	// 加载资源类配置（optional）
	env.LoadConf("resource.yaml", env.SubConfMount, &RConf)

	// 加载业务类(需要通过配置中心可修改的业务类配置)配置 （optional）
	// ... 加载更多配置
	env.LoadConf("custom.yaml", env.SubConfMount, &Custom)
}

func GetAppName() string {
	return App
}
