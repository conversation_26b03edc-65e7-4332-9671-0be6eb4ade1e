{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE HTML>
<head>
    <title>流量diff详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
<meta charset="UTF-8">

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">diff 结果查询</h3>
    <div class="form-group">
        <label for="timeRange">时间范围{{.param.TimeRange}}</label>
        <select class="form-control" id="timeRange" name="timeRange" >
            <option value=1 {{if eq .params.TimeRange 1}}selected{{end}}>一天内的数据</option>
            <option value=2 {{if eq .params.TimeRange 2}}selected{{end}}>两天内的数据</option>
            <option value=3 {{if eq .params.TimeRange 3}}selected{{end}}>三天内的数据</option>
            <option value=4 {{if eq .params.TimeRange 3}}selected{{end}}>四天内的数据</option>
            <option value=5 {{if eq .params.TimeRange 3}}selected{{end}}>五天内的数据</option>

        </select>

        <label for="handler">接口名 后缀0:新老diff，后缀1:环比diff</label>
        <select class="form-control" id="handler" name="handler" >
            <option value="StudentList_0" {{if eq .params.Handler "StudentList_0"}}selected{{end}}>StudentList_0</option>
            <option value="StudentList_1" {{if eq .params.Handler "StudentList_1"}}selected{{end}}>StudentList_1</option>
            <option value="CourseListCard_0" {{if eq .params.Handler "CourseListCard_0"}}selected{{end}}>CourseListCard_0</option>
            <option value="GetFilterMap_0" {{if eq .params.Handler "GetFilterMap_0"}}selected{{end}}>GetFilterMap_0</option>
            <option value="Collection_0" {{if eq .params.Handler "Collection_0"}}selected{{end}}>Collection_0</option>
            <option value="LessonInfo_0" {{if eq .params.Handler "LessonInfo_0"}}selected{{end}}>LessonInfo_0</option>
            <option value="Get_0" {{if eq .params.Handler "Get_0"}}selected{{end}}>Get_0</option>
            <option value="GetFieldMapTree_0" {{if eq .params.Handler "GetFieldMapTree_0"}}selected{{end}}>GetFieldMapTree_0</option>


        </select>
    </div>


    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>


<ul class="nav nav-tabs" id="tabService">
    <li role="presentation" class="active" >
        <a class="chapterTaskList">diff结果 展示前 100 条有 diff 的详细记录</a>
    </li>
</ul>


<div style="overflow-y: scroll; margin-top: 8px; margin-right: 10px; text-align: center;">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px; display: inline-block;">
        <tr>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>总 diff 任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #FF0000;color:white'>有 diff 的任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>无 diff 的任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>未回放完成的任务数</th>
        </tr>
        <tr style="height: 46px">
            <td>{{.data.Total}}</td>
            <td>{{.data.HasDiffNum}}</td>
            <td>{{.data.NoDiffNum}}</td>
            <td>{{.data.UnFinishedTask}}</td>
        </tr>
    </table>
</div>

<!--<td>
    <a href="/arkgo/tool/testdiffstudentdata" style="color: red;" onclick="executeAction(event)">手动执行</a><br>
    <span id="data"></span>
</td>-->
<br><br>

<script>
    function executeAction(event) {
        event.preventDefault();

        fetch('/arkgo/tool/testdiffstudentdata')
            .then(response => response.text())
            .then(data => {
                // 这里可以对数据进行处理，例如格式化或其他操作
                document.getElementById("data").innerText = data;
            })
            .catch(error => console.error('Error:', error));
    }
</script>




<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
        <tbody>
        <tr style="background-color: #d9d9d9">
            <th style="width:200px">请求参数</th>
            <th style="width:100px">diff数</th>
            <th style="width:200px">before返回值</th>
            <th style="width:200px">new返回值</th>
            <th style="width:120px">diff详情</th>
            <th style="width:120px">接口名</th>
            <th style="width:230px">更新时间</th>
            <th style="width:230px">创建时间</th>
        </tr>
        {{range $key, $diffInfo := .data.DataDiffList}}
        <tr style="height: 46px; font-size: 14px;">
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.Params}}</textarea></td>
            <td>{{$diffInfo.DiffNum}}</td>
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.OldData}}</textarea></td>
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.NewData}}</textarea></td>
            <td><a href="{{$diffInfo.DiffResult}}" style="color: red;" target="_blank">Diff结果</a></td>
            <td>{{$diffInfo.HandlerName}}</td>
            <td>{{showTime $diffInfo.UpdateTime}}</td>
            <td>{{showTime $diffInfo.CreateTime}}</td>
        </tr>
        {{end}}
        </tbody>
    </table>
</div>

{{end}}


