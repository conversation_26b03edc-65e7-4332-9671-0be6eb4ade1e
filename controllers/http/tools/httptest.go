package tools

import (
	"fwyytool/components"
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/service/tools"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var HttpTestController httpTestController

type httpTestController struct {
}

func (s httpTestController) Call(ctx *gin.Context) {
	var params toolsInput.HttpTestCallParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := tools.HttpTestService.Call(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (s httpTestController) Router(ctx *gin.Context) {
	var params toolsInput.HttpTestRouterParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err = tools.HttpTestService.Router(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 注意：Router方法直接写入响应，不需要再次调用RenderJsonSucc
	// 响应状态码和内容已经在service层设置
}

func (s httpTestController) ClearQWUrlCache(ctx *gin.Context) {
	var params toolsInput.HttpTestClearQWUrlCacheParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := tools.ClearQWCache(ctx, params.MinID, params.MaxID)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
	return
}
