package desk

import (
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"github.com/gin-gonic/gin"
	"net/http"
)

var DataDiffController dataDiffController

type dataDiffController struct {
}

func (s dataDiffController) DataDiffDetail(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		TimeRange int64  `json:"timeRange" form:"timeRange" `
		<PERSON><PERSON>   string `json:"handler" form:"handler"`
	}

	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkDataDiffService.GetDiffRes(ctx, params.TimeRange, params.Handler)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", output)
	return
}
