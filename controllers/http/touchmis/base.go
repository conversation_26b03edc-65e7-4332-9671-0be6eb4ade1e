package touchmis

import (
	"fwyytool/controllers/http/touchmis/tools/kms"
	"fwyytool/controllers/http/touchmis/tools/kp"
	"fwyytool/controllers/http/touchmis/tools/touch"
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	deskRouterGroup := rg.Group("touchmis")
	toolsRouterGroup := deskRouterGroup.Group("tools")
	{
		kmsRouterGroup := toolsRouterGroup.Group("kms")
		{
			kmsRouterGroup.GET("decryptAndEncrypt", kms.DecryptAndEncrypt)
		}

		kpRouterGroup := toolsRouterGroup.Group("kp")
		{
			kpRouterGroup.GET("checkFriend", kp.CheckFriend)
			kpRouterGroup.GET("getSendQueueLength", kp.GetSendQueueLength)
		}

		touchRouterGroup := toolsRouterGroup.Group("touch")
		{
			touchRouterGroup.GET("getCallRecordInfo", touch.GetCallRecordInfo)
		}
	}
}
